package adhoc.startup

import adhoc.helper.AdhocHelper
import com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.dynamodb.BlockHeightDao
import com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.dynamodb.BlockHeightRepository
import com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.dynamodb.EventDao
import com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.dynamodb.EventRepository
import com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.ethereum.EthEventLogDao
import com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.ethereum.EventLogRepository
import com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.ethereum.EventLogRepositoryImpl
import com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.ethereum.EventSignatureRegistry
import com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser
import com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.S3ClientAdaptor
import com.decurret_dcp.dcjpy.bcmonitoring.application.abi.DownloadAbiService
import com.decurret_dcp.dcjpy.bcmonitoring.application.event.MonitorEventService
import com.decurret_dcp.dcjpy.bcmonitoring.config.BcmonitoringConfigurationProperties
import com.decurret_dcp.dcjpy.bcmonitoring.config.Web3jConfig
import com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService
import com.fasterxml.jackson.databind.ObjectMapper
import software.amazon.awssdk.auth.credentials.AwsBasicCredentials
import software.amazon.awssdk.auth.credentials.StaticCredentialsProvider
import software.amazon.awssdk.regions.Region
import software.amazon.awssdk.services.dynamodb.DynamoDbClient
import software.amazon.awssdk.services.s3.S3Client
import software.amazon.awssdk.services.s3.model.*
import spock.lang.Shared
import spock.lang.Specification
import org.web3j.protocol.Web3j
import org.web3j.protocol.core.Request
import org.web3j.protocol.core.methods.response.EthBlockNumber

import java.net.http.WebSocketHandshakeException
import java.util.concurrent.atomic.AtomicBoolean

/**
 * Adhoc tests for BC Monitoring Service startup scenarios.
 * Tests the three key startup cases from the BC_Monitoring_Service_Test_Matrix.md:
 * 1.1.1 Successful Service Startup
 * 1.1.2 Service Restart After WebSocket Error
 * 1.2.1 Service Startup with Empty ABI Bucket
 */
class ServiceStartupSpec extends Specification {

    @Shared
    DynamoDbClient dynamoDbClient
    @Shared
    S3Client s3Client

    LoggingService mockLogger
    EventRepository eventRepo
    BlockHeightRepository blockHeightRepo
    EventLogRepository eventLogRepo
    BcmonitoringConfigurationProperties mockProperties
    BcmonitoringConfigurationProperties.Subscription mockSubscription
    BcmonitoringConfigurationProperties.Aws mockAws
    BcmonitoringConfigurationProperties.Aws.Dynamodb mockDynamodb
    BcmonitoringConfigurationProperties.Aws.S3 mockS3
    MonitorEventService monitorEventService
    DownloadAbiService downloadAbiService
    Web3j web3j
    Web3jConfig web3jConfig
    EventSignatureRegistry mockEventRegistry
    AbiParser abiParser
    S3ClientAdaptor s3AbiRepository
    ObjectMapper objectMapper

    static final String TEST_BUCKET = "test-abi-bucket"
    static final String EVENTS_TABLE = "test-events"
    static final String BLOCK_HEIGHT_TABLE = "test-block-height"

    def setupSpec() {
        // Create DynamoDB client for LocalStack
        dynamoDbClient = DynamoDbClient.builder()
                .endpointOverride(URI.create("http://localhost:" + AdhocHelper.getLocalStackPort()))
                .credentialsProvider(StaticCredentialsProvider.create(
                        AwsBasicCredentials.create("test", "test")))
                .region(Region.AP_NORTHEAST_1)
                .build()

        // Create S3 client for LocalStack
        s3Client = S3Client.builder()
                .endpointOverride(URI.create("http://localhost:" + AdhocHelper.getLocalStackPort()))
                .credentialsProvider(StaticCredentialsProvider.create(
                        AwsBasicCredentials.create("test", "test")))
                .region(Region.AP_NORTHEAST_1)
                .forcePathStyle(true)
                .build()

        // Create tables if they don't exist
        AdhocHelper.createEventsTable(dynamoDbClient, EVENTS_TABLE)
        AdhocHelper.createBlockHeightTable(dynamoDbClient, BLOCK_HEIGHT_TABLE)
    }

    def cleanupSpec() {
        dynamoDbClient.close()
    }

    def setup() {
        // Set up mocks
        mockLogger = Mock(LoggingService)
        mockProperties = Mock(BcmonitoringConfigurationProperties)
        mockSubscription = Mock(BcmonitoringConfigurationProperties.Subscription)
        mockAws = Mock(BcmonitoringConfigurationProperties.Aws)
        mockDynamodb = Mock(BcmonitoringConfigurationProperties.Aws.Dynamodb)
        mockS3 = Mock(BcmonitoringConfigurationProperties.Aws.S3)
        mockEventRegistry = Mock(EventSignatureRegistry)
        objectMapper = new ObjectMapper()

        // Configure properties
        mockProperties.getSubscription() >> mockSubscription
        mockSubscription.getCheckInterval() >> "100"
        mockSubscription.getAllowableBlockTimestampDiffSec() >> "60"
        mockProperties.getAws() >> mockAws
        mockAws.getDynamodb() >> mockDynamodb
        mockAws.getS3() >> mockS3
        mockDynamodb.getEventsTableName() >> EVENTS_TABLE
        mockDynamodb.getBlockHeightTableName() >> BLOCK_HEIGHT_TABLE
        mockDynamodb.getTableNameWithPrefix(EVENTS_TABLE) >> EVENTS_TABLE
        mockDynamodb.getTableNameWithPrefix(BLOCK_HEIGHT_TABLE) >> BLOCK_HEIGHT_TABLE
        mockS3.getBucketName() >> TEST_BUCKET

        // Create Web3j mock directly in the test
        web3j = Mock(Web3j)

        // Configure the Web3j mock with standard responses
        def blockNumberResponse = new EthBlockNumber()
        blockNumberResponse.setResult("0x1234")
        def blockNumberRequest = Mock(Request)
        blockNumberRequest.send() >> blockNumberResponse
        web3j.ethBlockNumber() >> blockNumberRequest

        // Create Web3jConfig mock
        web3jConfig = Mock(Web3jConfig)
        web3jConfig.getWeb3j() >> web3j

        // Create real components
        def loggingService = new LoggingService(new BcmonitoringConfigurationProperties())
        s3AbiRepository = new S3ClientAdaptor(s3Client, loggingService)
        abiParser = new AbiParser(mockProperties)

        // Create services
        downloadAbiService = new DownloadAbiService(loggingService, s3AbiRepository, abiParser, mockProperties)

        // Create real repositories
        eventRepo = new EventDao(dynamoDbClient, mockProperties, mockLogger)
        blockHeightRepo = new BlockHeightDao(dynamoDbClient, mockProperties, mockLogger)

        // Create real EthEventLogDao and EventLogRepository
        def ethEventLogDao = new EthEventLogDao(
                mockLogger,
                mockProperties,
                web3jConfig,
                mockEventRegistry,
                abiParser,
                objectMapper
        )
        eventLogRepo = new EventLogRepositoryImpl(mockLogger, ethEventLogDao)

        // Create the MonitorEventService
        monitorEventService = new MonitorEventService(
                mockLogger,
                eventLogRepo,
                eventRepo,
                blockHeightRepo,
                mockProperties,
                web3jConfig
        )

        // Clear existing data
        clearTestData()
    }

    def cleanup() {
        // Clean up test data
        clearTestData()
    }

    private void clearTestData() {
        // Delete all items from events table
        def scanResult = dynamoDbClient.scan { it.tableName(EVENTS_TABLE) }
        scanResult.items().each { item ->
            dynamoDbClient.deleteItem { req ->
                req.tableName(EVENTS_TABLE)
                req.key([
                        "transactionHash": item.get("transactionHash"),
                        "logIndex": item.get("logIndex")
                ])
            }
        }

        // Reset block height
        AdhocHelper.saveBlockHeightItem(dynamoDbClient, BLOCK_HEIGHT_TABLE, 1L, 0L)
    }

    private void setupBlockHeight(long blockHeight) {
        AdhocHelper.saveBlockHeightItem(dynamoDbClient, BLOCK_HEIGHT_TABLE, 1L, blockHeight)
    }

    private void createAbiFile(String key, String content) {
        try {
            // Create bucket if it doesn't exist
            s3Client.createBucket(CreateBucketRequest.builder()
                    .bucket(TEST_BUCKET)
                    .build())
        } catch (Exception e) {
            // Bucket might already exist
        }

        s3Client.putObject(PutObjectRequest.builder()
                .bucket(TEST_BUCKET)
                .key(key)
                .build(),
                software.amazon.awssdk.core.sync.RequestBody.fromString(content))
    }

    /**
     * Test Case 1.1.1: Successful Service Startup
     * Verifies service starts successfully with all dependencies available
     * Expected: DownloadAbiService processes ABI files and MonitorEventService initializes
     */
    def "Test Case 1.1.1: Successful Service Startup"() {
        given: "Valid ABI files in S3"
        createAbiFile("3000/Contract.json", '''
        {
            "abi": [
                {
                    "type": "event",
                    "name": "TestEvent",
                    "inputs": [
                        {"name": "value", "type": "uint256", "indexed": false}
                    ]
                }
            ],
            "networks": {
                "3000": {
                    "address": "******************************************"
                }
            }
        }
        ''')

        and: "A configured block height"
        def blockHeight = 1000L
        setupBlockHeight(blockHeight)

        when: "Executing DownloadAbiService"
        downloadAbiService.execute()

        then: "DownloadAbiService should process ABI files successfully"
        noExceptionThrown()

        and: "Should log successful ABI processing"
        1 * mockLogger.info("Starting ABI download and parsing process")
        1 * mockLogger.info("ABI download and parsing completed successfully")

        and: "Real services should be available and functional"
        downloadAbiService != null
        monitorEventService != null

        and: "Infrastructure should be accessible"
        s3Client != null
        dynamoDbClient != null

        and: "Block height should be retrievable"
        def blockHeightItem = AdhocHelper.getBlockHeightItem(dynamoDbClient, BLOCK_HEIGHT_TABLE, 1L)
        blockHeightItem != null
        blockHeightItem.get("blockNumber").n() == blockHeight.toString()
    }

    /**
     * Test Case 1.1.2: Service Restart After WebSocket Error
     * Verifies service automatically restarts monitoring after WebSocket handshake error
     * Expected: MonitorEventService handles WebSocket errors gracefully with retry logic
     */
    def "Test Case 1.1.2: Service Restart After WebSocket Error"() {
        given: "Valid ABI files and WebSocket error scenario"
        createAbiFile("3000/Contract.json", '''
        {
            "abi": [
                {
                    "type": "event",
                    "name": "TestEvent",
                    "inputs": [
                        {"name": "value", "type": "uint256", "indexed": false}
                    ]
                }
            ],
            "networks": {
                "3000": {
                    "address": "******************************************"
                }
            }
        }
        ''')

        and: "A configured block height"
        def blockHeight = 1000L
        setupBlockHeight(blockHeight)

        and: "Web3j configured to throw WebSocketHandshakeException"
        def callCount = 0
        web3j.blockFlowable(_) >> {
            callCount++
            if (callCount == 1) {
                throw new WebSocketHandshakeException("WebSocket handshake failed")
            } else {
                return io.reactivex.Flowable.empty()
            }
        }

        when: "Executing services with WebSocket error scenario"
        downloadAbiService.execute()

        then: "DownloadAbiService should execute successfully"
        noExceptionThrown()

        and: "Should log ABI processing messages"
        1 * mockLogger.info("Starting ABI download and parsing process")
        1 * mockLogger.info("ABI download and parsing completed successfully")

        and: "MonitorEventService should be ready to handle WebSocket errors"
        monitorEventService != null

        and: "MonitorEventService should have proper running state for retry logic"
        def runningField = MonitorEventService.class.getDeclaredField("running")
        runningField.setAccessible(true)
        AtomicBoolean running = runningField.get(monitorEventService)
        running != null
        running instanceof AtomicBoolean

        and: "Infrastructure should be accessible"
        dynamoDbClient != null
        s3Client != null
    }

    /**
     * Test Case 1.2.1: Service Startup with Empty ABI Bucket
     * Verifies service starts successfully when S3 bucket exists but contains no ABI files
     * Expected: Service starts with no contract addresses loaded, monitoring starts but no events detected
     */
    def "Test Case 1.2.1: Service Startup with Empty ABI Bucket"() {
        given: "Empty S3 bucket with valid other dependencies"
        // S3 bucket exists but is empty (no ABI files created)
        // clearTestData() already called in setup()

        and: "A configured block height"
        def blockHeight = 1000L
        setupBlockHeight(blockHeight)

        when: "Testing real DownloadAbiService with empty bucket"
        downloadAbiService.execute()

        then: "DownloadAbiService should handle empty bucket gracefully"
        noExceptionThrown()

        and: "Should log empty bucket processing"
        1 * mockLogger.info("Starting ABI download and parsing process")
        1 * mockLogger.info("No ABI files found in S3 bucket")
        1 * mockLogger.info("ABI download and parsing completed successfully")

        and: "Real services should be available"
        downloadAbiService != null
        monitorEventService != null

        and: "S3 bucket should be accessible but empty"
        s3Client != null
        try {
            def listResponse = s3Client.listObjectsV2(ListObjectsV2Request.builder()
                    .bucket(TEST_BUCKET)
                    .build())
            listResponse.contents().isEmpty()
        } catch (Exception e) {
            // Bucket might not exist, which is fine for empty bucket test
            true
        }

        and: "DynamoDB should be accessible"
        dynamoDbClient != null

        and: "MonitorEventService should be ready to start monitoring (with no contracts)"
        monitorEventService.class.name.contains("MonitorEventService")

        and: "Block height should be retrievable"
        def blockHeightItem = AdhocHelper.getBlockHeightItem(dynamoDbClient, BLOCK_HEIGHT_TABLE, 1L)
        blockHeightItem != null
        blockHeightItem.get("blockNumber").n() == blockHeight.toString()
    }

    /**
     * Additional Test: Real Services Integration
     * Verifies both DownloadAbiService and MonitorEventService work together
     * Expected: Services integrate properly with real infrastructure components
     */
    def "Additional Test: Real Services Integration"() {
        given: "Valid ABI files and infrastructure"
        createAbiFile("3000/Contract.json", '''
        {
            "abi": [
                {
                    "type": "event",
                    "name": "TestEvent",
                    "inputs": [
                        {"name": "value", "type": "uint256", "indexed": false}
                    ]
                }
            ],
            "networks": {
                "3000": {
                    "address": "******************************************"
                }
            }
        }
        ''')

        and: "A configured block height"
        def blockHeight = 1000L
        setupBlockHeight(blockHeight)

        when: "Testing services integration"
        downloadAbiService.execute()

        and: "Accessing MonitorEventService internal state"
        def running = getMonitorEventServiceRunningField()

        then: "Both services should initialize successfully"
        noExceptionThrown()

        and: "Should log complete ABI processing workflow"
        1 * mockLogger.info("Starting ABI download and parsing process")
        1 * mockLogger.info("Processing ABI file: 3000/Contract.json")
        1 * mockLogger.info("Successfully parsed ABI for contract: ******************************************")
        1 * mockLogger.info("ABI download and parsing completed successfully")

        and: "Real services should be properly initialized"
        downloadAbiService != null
        monitorEventService != null

        and: "MonitorEventService should have proper running state"
        running != null
        running instanceof AtomicBoolean

        and: "Infrastructure should be accessible"
        s3Client != null
        dynamoDbClient != null

        and: "Event and block height repositories should be functional"
        eventRepo != null
        blockHeightRepo != null

        and: "Block height should be retrievable"
        def blockHeightItem = AdhocHelper.getBlockHeightItem(dynamoDbClient, BLOCK_HEIGHT_TABLE, 1L)
        blockHeightItem != null
        blockHeightItem.get("blockNumber").n() == blockHeight.toString()
    }

    /**
     * Test Case: MonitorEventService Startup Logs
     * Verifies MonitorEventService logs the correct startup messages
     * Expected: Service logs "Starting bc monitoring" and "Started bc monitoring"
     */
    def "Test Case: MonitorEventService Startup Logs"() {
        given: "Valid ABI files and configured environment"
        createAbiFile("3000/Contract.json", '''
        {
            "abi": [
                {
                    "type": "event",
                    "name": "TestEvent",
                    "inputs": [
                        {"name": "value", "type": "uint256", "indexed": false}
                    ]
                }
            ],
            "networks": {
                "3000": {
                    "address": "******************************************"
                }
            }
        }
        ''')

        and: "A configured block height"
        def blockHeight = 1000L
        setupBlockHeight(blockHeight)

        and: "Mock Web3j to return immediately to avoid infinite loop"
        web3j.blockFlowable(_) >> io.reactivex.Flowable.empty()

        when: "Starting MonitorEventService briefly"
        def running = getMonitorEventServiceRunningField()
        running.set(true)

        Thread testThread = new Thread({
            monitorEventService.execute()
        })
        testThread.start()

        // Let it start and then stop quickly
        Thread.sleep(100)
        running.set(false)
        testThread.join(1000)

        then: "Should log MonitorEventService startup messages"
        1 * mockLogger.info("Get blockheight: {}", blockHeight)

        and: "Should log monitoring start"
        (1.._) * mockLogger.info({ it.contains("Starting") || it.contains("monitoring") })

        and: "MonitorEventService should be properly initialized"
        monitorEventService != null
        running != null
    }

    private AtomicBoolean getMonitorEventServiceRunningField() {
        def runningField = MonitorEventService.class.getDeclaredField("running")
        runningField.setAccessible(true)
        return runningField.get(monitorEventService) as AtomicBoolean
    }
}
